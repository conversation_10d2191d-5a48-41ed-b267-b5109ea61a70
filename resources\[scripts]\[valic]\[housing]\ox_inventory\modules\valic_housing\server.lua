-- Valic Housing Integration for ox_inventory
-- This module provides housing-specific inventory functionality

local Inventory = require 'modules.inventory.server'

-- Check if valic_housing resource is available
local function isValicHousingAvailable()
    return GetResourceState('valic_housing') == 'started'
end

-- Get house storage identifier
local function getHouseStorageId(houseId)
    return ('house_%s'):format(houseId)
end

-- Create house storage inventory
local function createHouseStorage(houseId, slots, weight)
    if not isValicHousingAvailable() then return false end
    
    local storageId = getHouseStorageId(houseId)
    local house = exports.valic_housing:GetHouseById(houseId)
    
    if not house then return false end
    
    local inventory = Inventory.Create(storageId, house.property_name or ('House %s'):format(houseId), 'stash', slots or 50, weight or 100000, false)
    
    return inventory ~= nil
end

-- Check if player can access house storage
local function canAccessHouseStorage(playerId, houseId)
    if not isValicHousingAvailable() then return false end
    
    local player = Ox.GetPlayer(playerId)
    if not player then return false end
    
    local citizenId = player.get('citizenid') or player.get('charId')
    if not citizenId then return false end
    
    -- Check if player owns the house or has keys
    local isOwner = exports.valic_housing:IsHouseOwner(houseId, citizenId)
    local hasKeys = exports.valic_housing:HasHouseKeys(houseId, citizenId)
    
    return isOwner or hasKeys
end

-- Open house storage for player
local function openHouseStorage(playerId, houseId, slots, weight)
    if not canAccessHouseStorage(playerId, houseId) then
        return false, 'You do not have access to this house storage'
    end
    
    local storageId = getHouseStorageId(houseId)
    
    -- Create storage if it doesn't exist
    if not Inventory(storageId) then
        if not createHouseStorage(houseId, slots, weight) then
            return false, 'Failed to create house storage'
        end
    end
    
    -- Open inventory for player
    local success = Inventory.OpenInventory(playerId, 'stash', storageId)
    
    return success, success and 'House storage opened' or 'Failed to open house storage'
end

-- Get house storage inventory
local function getHouseStorage(houseId)
    local storageId = getHouseStorageId(houseId)
    return Inventory(storageId)
end

-- Clear house storage (when house is sold/reset)
local function clearHouseStorage(houseId)
    local storageId = getHouseStorageId(houseId)
    local inventory = Inventory(storageId)
    
    if inventory then
        -- Clear all items
        inventory:clear()
        return true
    end
    
    return false
end

-- Export functions for other resources
exports('createHouseStorage', createHouseStorage)
exports('openHouseStorage', openHouseStorage)
exports('getHouseStorage', getHouseStorage)
exports('clearHouseStorage', clearHouseStorage)
exports('canAccessHouseStorage', canAccessHouseStorage)

-- Register callback for opening house storage
lib.callback.register('valic_housing:openStorage', function(source, houseId, slots, weight)
    return openHouseStorage(source, houseId, slots, weight)
end)

-- Event handlers for house events
if isValicHousingAvailable() then
    -- Clear storage when house is sold
    RegisterNetEvent('valic_housing:server:HouseSold', function(houseId)
        clearHouseStorage(houseId)
    end)
    
    -- Clear storage when house is reset
    RegisterNetEvent('valic_housing:server:HouseReset', function(houseId)
        clearHouseStorage(houseId)
    end)
    
    -- Clear storage when all houses are reset
    RegisterNetEvent('valic_housing:server:AllHousesReset', function()
        if not exports.valic_housing then return end
        
        local houses = exports.valic_housing:GetAllHouses()
        for _, house in pairs(houses) do
            clearHouseStorage(house.id)
        end
    end)
end

-- Hook into inventory events for logging
if shared.framework == 'qb' or shared.framework == 'qbx' then
    -- Register hook for item movement in house storage
    exports.ox_inventory:registerHook('swapItems', function(payload)
        if not payload.fromInventory or not payload.toInventory then return end
        
        local fromType = payload.fromInventory:match('^house_(%d+)$')
        local toType = payload.toInventory:match('^house_(%d+)$')
        
        if fromType or toType then
            -- This is a house storage transaction
            local houseId = fromType or toType
            local player = Ox.GetPlayer(payload.source)
            
            if player then
                local citizenId = player.get('citizenid') or player.get('charId')
                
                -- Log the transaction (you can customize this)
                print(('[valic_housing] Player %s (%s) moved items in house %s storage'):format(
                    player.get('name') or 'Unknown',
                    citizenId or 'Unknown',
                    houseId
                ))
            end
        end
    end, {
        print = false -- Set to true for debugging
    })
end

return {
    createHouseStorage = createHouseStorage,
    openHouseStorage = openHouseStorage,
    getHouseStorage = getHouseStorage,
    clearHouseStorage = clearHouseStorage,
    canAccessHouseStorage = canAccessHouseStorage
}
