-- Valic Housing Integration for ox_inventory (Client)
-- This module provides client-side housing inventory functionality

-- Check if valic_housing resource is available
local function isValicHousingAvailable()
    return GetResourceState('valic_housing') == 'started'
end

-- Open house storage
local function openHouseStorage(houseId, slots, weight)
    if not isValicHousingAvailable() then
        lib.notify({
            title = 'Error',
            description = 'Housing system is not available',
            type = 'error'
        })
        return false
    end
    
    lib.callback('valic_housing:openStorage', false, function(success, message)
        if not success then
            lib.notify({
                title = 'Storage Access',
                description = message or 'Failed to access storage',
                type = 'error'
            })
        end
    end, houseId, slots, weight)
    
    return true
end

-- Export for other resources
exports('openHouseStorage', openHouseStorage)

-- Integration with valic_housing events
if isValicHousingAvailable() then
    -- Add storage option to house admin panel
    RegisterNetEvent('valic_housing:client:ShowAdminPanel', function(houseId, options)
        -- Add storage option to the admin panel
        table.insert(options, {
            title = 'House Storage',
            description = 'Access your house storage',
            icon = 'box',
            onSelect = function()
                openHouseStorage(houseId)
            end
        })
    end)
    
    -- Add storage access for key holders
    RegisterNetEvent('valic_housing:client:ShowKeyHolderPanel', function(houseId, options)
        -- Add storage option for key holders
        table.insert(options, {
            title = 'House Storage',
            description = 'Access house storage',
            icon = 'box',
            onSelect = function()
                openHouseStorage(houseId)
            end
        })
    end)
end

return {
    openHouseStorage = openHouseStorage
}
